"""
Celery tasks for Google Drive integration.
"""
from concurrent.futures import ThreadPoolExecutor

from app.modules.connectors.handlers.gdrive.services.google_drive_service import (
    GoogleDriveService,
)
from app.modules.connectors.workers.celery_worker import app


@app.task
def sync_folders_by_ids(folder_ids):
    """
    Synchronizes a list of Google Drive folders by their IDs.

    Args:
        folder_ids (list): A list of Google Drive folder IDs to synchronize.
    """
    with ThreadPoolExecutor(max_workers=4) as executor:
        # sync_folder_by_id is not defined; placeholder logic
        results = [f"Simulated sync for folder {fid}" for fid in folder_ids]
    return results


@app.task
def sync_drive_task(organisation_id, full_sync=False):
    """
    Synchronizes an entire Google Drive for a given organization.

    Args:
        organisation_id (str): The ID of the organization to sync.
        full_sync (bool, optional): Whether to perform a full sync. Defaults to False.
    """
    from app.modules.connectors.handlers.gdrive.services.google_drive_service import (
        GoogleDriveService,
    )

    service = GoogleDriveService()
    return service.sync_drive(organisation_id, full_sync)


@app.task
def sync_file_by_id_task(
    file_id, agent_id, user_id, organisation_id, url=None
):
    """
    Synchronizes a single file by its ID.

    Args:
        file_id (str): The ID of the file to synchronize.
        agent_id (str): The ID of the agent initiating the sync.
        user_id (str): The ID of the user who owns the file.
        organisation_id (str): The ID of the organization.
        url (str, optional): The URL of the file. Defaults to None.
    """
    service = GoogleDriveService()
    return service.sync_file_by_id(
        file_id, agent_id, user_id, organisation_id, url
    )


@app.task
def sync_folder_recursively_task(organisation_id, folder_id):
    """
    Recursively synchronizes a folder and its contents.

    Args:
        organisation_id (str): The ID of the organization.
        folder_id (str): The ID of the folder to synchronize.
    """
    service = GoogleDriveService()
    drive = service.get_service_account_drive_service(organisation_id)
    return service.sync_folder_recursively_with_permissions(
        drive, organisation_id, folder_id
    )
