from app.modules.connectors.workers.celery_worker import app
from app.modules.connectors.workers.tasks import sync_drive_task, sync_file_by_id_task

if __name__ == "__main__":
    # Use the configured Celery app
    task = sync_drive_task.delay(
        organisation_id="1e95f0df-4a35-4fdd-bc28-6ac681bf6675",
        full_sync=True,
        task_id="job-009",
    )
    print(task.get())

    # # file_id, agent_id, user_id, organisation_id, url, task_id
    # task = sync_file_by_id_task.delay(
    #     organisation_id="1e95f0df-4a35-4fdd-bc28-6ac681bf6675",
    #     file_id="",
    #     task_id="job-009",
    #     user_id="54c1059e-c891-4163-bbe6-1e5ad75601e1",
    # )
    # print(task.get())
